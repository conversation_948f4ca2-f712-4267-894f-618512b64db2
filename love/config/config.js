const path = require('path');
const fs = require('fs');

// 加载环境变量
require('dotenv').config({ path: path.join(__dirname, '.env') });

/**
 * Love项目配置管理模块
 * 统一管理所有配置项，支持环境变量覆盖
 */

// 配置验证函数
function validateConfig() {
    const errors = [];

    // 验证端口号
    const port = parseInt(process.env.PORT);
    if (isNaN(port) || port < 1 || port > 65535) {
        errors.push('PORT must be a valid port number (1-65535)');
    }

    // 验证域名
    if (!process.env.BASE_DOMAIN) {
        errors.push('BASE_DOMAIN is required');
    }

    // 验证数据库路径
    if (!process.env.DB_PATH) {
        errors.push('DB_PATH is required');
    }

    // 验证Cloudinary配置 (如果启用)
    if (process.env.CLOUDINARY_ENABLED === 'true') {
        const requiredSecrets = [
            'CLOUDINARY_SECRET_YU0',
            'CLOUDINARY_SECRET_YU1',
            'CLOUDINARY_SECRET_YU2',
            'CLOUDINARY_SECRET_YU3',
            'CLOUDINARY_SECRET_YU4',
            'CLOUDINARY_SECRET_YU5'
        ];

        requiredSecrets.forEach(secret => {
            if (!process.env[secret]) {
                errors.push(`${secret} is required when Cloudinary is enabled`);
            }
        });
    }

    if (errors.length > 0) {
        console.error('Configuration validation errors:');
        errors.forEach(error => console.error(`  - ${error}`));
        throw new Error('Invalid configuration');
    }
}

// 主配置对象
const config = {
    // 服务器配置 - 直接使用.env配置
    server: {
        port: parseInt(process.env.PORT) || 1314,
        env: process.env.NODE_ENV || 'production',
        corsOrigin: process.env.CORS_ORIGIN || '*',
        staticFilesCache: parseInt(process.env.STATIC_FILES_CACHE) || 3600
    },

    // 域名配置 - 硬编码配置（统一域名管理）
    domain: {
        // 基础域名配置（硬编码）
        base: 'love.yuh.cool',
        url: 'https://love.yuh.cool',

        // API相关URL配置
        api: {
            base: 'https://love.yuh.cool/api',
            messages: 'https://love.yuh.cool/api/messages',
            timeline: 'https://love.yuh.cool/api/timeline',
            memories: 'https://love.yuh.cool/api/memories',
            modernQuotes: 'https://love.yuh.cool/api/modern-quotes',
            health: 'https://love.yuh.cool/api/health',
            config: 'https://love.yuh.cool/api/config'
        },

        // 页面路径配置
        pages: {
            home: 'https://love.yuh.cool/',
            togetherDays: 'https://love.yuh.cool/together-days',
            anniversary: 'https://love.yuh.cool/anniversary',
            meetings: 'https://love.yuh.cool/meetings',
            memorial: 'https://love.yuh.cool/memorial',
            verify: 'https://love.yuh.cool/verify'
        },

        // 静态资源路径配置
        assets: {
            base: 'https://love.yuh.cool/src/client',
            styles: 'https://love.yuh.cool/src/client/styles',
            scripts: 'https://love.yuh.cool/src/client/scripts',
            fonts: 'https://love.yuh.cool/src/client/assets/fonts',
            images: 'https://love.yuh.cool/src/client/assets/images',
            videos: 'https://love.yuh.cool/src/client/assets/videos'
        },

        // 兼容路径配置
        legacy: {
            html: 'https://love.yuh.cool/html',
            background: 'https://love.yuh.cool/background',
            fonts: 'https://love.yuh.cool/fonts',
            test: 'https://love.yuh.cool/test'
        },

        // 环境变量fallback（保持兼容性）
        env: {
            base: process.env.BASE_DOMAIN || 'love.yuh.cool',
            url: process.env.BASE_URL || 'https://love.yuh.cool'
        }
    },
    
    // 数据库配置 - 直接使用.env配置
    database: {
        path: process.env.DB_PATH || './data/love_messages.db',
        backupDir: process.env.DB_BACKUP_DIR || './data/backups'
    },

    // 日志配置 - 直接使用.env配置
    logging: {
        level: process.env.LOG_LEVEL || 'info',
        file: process.env.LOG_FILE || './logs/backend.log'
    },
    
    // 特殊日期配置 - 直接使用.env配置
    dates: {
        yuBirthday: process.env.YU_BIRTHDAY || '01-16',
        wangBirthday: process.env.WANG_BIRTHDAY || '04-15',
        loveStartDate: process.env.LOVE_START_DATE || '2023-01-01'
    },

    // API配置 - 直接使用.env配置
    api: {
        prefix: process.env.API_PREFIX || '/api',
        messagesEndpoint: process.env.MESSAGES_ENDPOINT || '/messages',
        healthEndpoint: process.env.HEALTH_ENDPOINT || '/health'
    },

    // Cloudinary多账户配置 (方案A - 高画质)
    cloudinary: {
        // 全局开关
        enabled: process.env.CLOUDINARY_ENABLED === 'true' || true,

        // 方案A压缩参数 (固定配置，不可修改)
        compression: {
            preset: 'extreme_quality',  // 方案A标识
            crf: 18,                    // 视觉无损质量
            speed: 'veryslow',          // 最佳压缩效率
            resolution: '2560:1440',    // 2K分辨率
            audioBitrate: '256k',       // 高质量音频
            pixelFormat: 'yuv420p',     // 兼容性像素格式
            profile: 'high',            // H.264高级配置
            level: '4.1'                // 兼容性级别
        },

        // 多账户配置 (严格按照现有架构)
        accounts: {
            INDEX: {
                cloudName: 'dcglebc2w',
                apiKey: '***************',
                apiSecret: process.env.CLOUDINARY_SECRET_YU0,
                folder: 'love-website',
                quota: 25 * 1024 * 1024 * 1024, // 25GB
                priority: 1
            },
            ANNIVERSARY: {
                cloudName: 'drhqbbqxz',
                apiKey: '***************',
                apiSecret: process.env.CLOUDINARY_SECRET_YU1,
                folder: 'love-website',
                quota: 25 * 1024 * 1024 * 1024,
                priority: 1
            },
            MEETINGS: {
                cloudName: 'dkqnm9nwr',
                apiKey: '***************',
                apiSecret: process.env.CLOUDINARY_SECRET_YU2,
                folder: 'love-website',
                quota: 25 * 1024 * 1024 * 1024,
                priority: 1
            },
            MEMORIAL: {
                cloudName: 'ds14sv2gh',
                apiKey: '822751152715929',
                apiSecret: process.env.CLOUDINARY_SECRET_YU3,
                folder: 'love-website',
                quota: 25 * 1024 * 1024 * 1024,
                priority: 1
            },
            TOGETHER_DAYS: {
                cloudName: 'dpq95x5nf',
                apiKey: '934251748658618',
                apiSecret: process.env.CLOUDINARY_SECRET_YU4,
                folder: 'love-website',
                quota: 25 * 1024 * 1024 * 1024,
                priority: 1
            },
            BACKUP: {
                cloudName: 'dtsgvqrna',
                apiKey: '567337797774118',
                apiSecret: process.env.CLOUDINARY_SECRET_YU5,
                folder: 'love-website',
                quota: 25 * 1024 * 1024 * 1024,
                priority: 2
            }
        },

        // 页面映射 (固定映射关系)
        pageMapping: {
            'home': 'INDEX',
            'anniversary': 'ANNIVERSARY',
            'meetings': 'MEETINGS',
            'memorial': 'MEMORIAL',
            'together-days': 'TOGETHER_DAYS'
        },

        // 加载器配置
        loader: {
            timeout: 15000,             // 15秒超时
            retries: 2,                 // 重试2次
            localFallback: true,        // 启用本地回退
            localPath: '/src/client/assets/videos-compressed',
            quality: 'q_auto:best,f_auto'  // Cloudinary高画质参数
        },

        // 清理配置
        cleanup: {
            enabled: true,              // 启用清理
            beforeUpload: true,         // 上传前清理
            mode: 'complete'            // 完全清理模式
        }
    },


};

// 配置验证（仅在非测试环境下执行）
if (process.env.NODE_ENV !== 'test') {
    try {
        validateConfig();
        console.log('✅ Configuration loaded successfully');
        console.log(`   Server: ${config.server.env} mode on port ${config.server.port}`);
        console.log(`   Domain: ${config.domain.url}`);
        console.log(`   Database: ${config.database.path}`);
    } catch (error) {
        console.error('❌ Configuration validation failed:', error.message);
        process.exit(1);
    }
}

// 前端配置暴露 (浏览器环境)
if (typeof window !== 'undefined') {
    window.loveConfig = config;
}

// 导出配置对象
module.exports = config;
